{"name": "node-odata-framework-ts", "version": "1.0.0", "description": "A TypeScript Node.js OData v4 framework for building REST APIs with OData query capabilities", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "npm run clean && tsc -p tsconfig.build.json", "build:watch": "tsc -p tsconfig.build.json --watch", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "nodemon --exec ts-node examples/express-app/server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts test/**/*.ts", "lint:fix": "eslint src/**/*.ts test/**/*.ts --fix", "typecheck": "tsc --noEmit", "prepare": "npm run build", "prepublishOnly": "npm run lint && npm run test && npm run build", "release:patch": "npm version patch && npm publish", "release:minor": "npm version minor && npm publish", "release:major": "npm version major && npm publish"}, "keywords": ["odata", "typescript", "rest", "api", "framework", "express", "middleware", "query", "filter"], "author": "Your Name <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/yourusername/node-odata-framework-ts.git"}, "engines": {"node": ">=14.0.0"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^4.18.2", "sequelize": "^6.37.7"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.42.0", "jest": "^29.5.0", "nodemon": "^3.0.1", "rimraf": "^5.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.1.3"}}