import { Sequelize, ModelAttributes, Model } from "sequelize";
import { IAttibutes, IDbConfig, IModelOptions } from "../types";

export class sequelizerAdaptor {
  private sequelize: Sequelize;

  constructor(dbConfig: IDbConfig) {
    this.sequelize = new Sequelize({
      database: dbConfig.database,
      username: dbConfig.username,
      password: dbConfig.password,
      host: dbConfig.host,
      dialect: dbConfig.dialect,
      port: dbConfig.port,
      pool: dbConfig.pool,
    });
  }

  public define(
    modelName: string,
    attributes: IAttibutes,
    options: IModelOptions
  ) {
    this.sequelize.define(modelName, attributes, options);
  }

  private formatAttributes(
    attributes: IAttibutes
  ): ModelAttributes<Model<any, any>> {
    return {};
  }
}
