import { IAttibutes, DataTypes } from "../types";
import { ModelAttributes, Model } from "sequelize";

// Create a standalone function to test the formatting logic
function formatAttributes(
  attributes: IAttibutes
): ModelAttributes<Model<any, any>> {
  const formattedAttributes: ModelAttributes<Model<any, any>> = {};

  for (const [attributeName, column] of Object.entries(attributes)) {
    formattedAttributes[attributeName] = {
      type: column.dataType,
      field: column.columnName || attributeName,
      primaryKey: column.isPrimaryKey || false,
      allowNull: column.isNullable !== false, // Default to true if not explicitly set to false
      unique: column.isUnique || false,
      autoIncrement: column.isAutoIncrement || false,
      defaultValue: column.defaultValue,
      onDelete: column.onDelete,
      onUpdate: column.onUpdate,
    };
  }

  return formattedAttributes;
}

describe("formatAttributes", () => {
  it("should convert IAttibutes to ModelAttributes correctly", () => {
    const testAttributes: IAttibutes = {
      id: {
        dataType: DataTypes.INTEGER,
        isPrimaryKey: true,
        isAutoIncrement: true,
        isNullable: false,
      },
      name: {
        dataType: DataTypes.STRING,
        columnName: "user_name",
        isUnique: true,
        isNullable: false,
      },
      email: {
        dataType: DataTypes.STRING,
        defaultValue: "<EMAIL>",
      },
      age: {
        dataType: DataTypes.INTEGER,
        isNullable: true,
      },
      status: {
        dataType: DataTypes.STRING,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      },
    };

    const result = formatAttributes(testAttributes);

    expect(result).toEqual({
        id: {
          type: DataTypes.INTEGER,
          field: "id",
          primaryKey: true,
          allowNull: false,
          unique: false,
          autoIncrement: true,
          defaultValue: undefined,
          onDelete: undefined,
          onUpdate: undefined,
        },
        name: {
          type: DataTypes.STRING,
          field: "user_name",
          primaryKey: false,
          allowNull: false,
          unique: true,
          autoIncrement: false,
          defaultValue: undefined,
          onDelete: undefined,
          onUpdate: undefined,
        },
        email: {
          type: DataTypes.STRING,
          field: "email",
          primaryKey: false,
          allowNull: true,
          unique: false,
          autoIncrement: false,
          defaultValue: "<EMAIL>",
          onDelete: undefined,
          onUpdate: undefined,
        },
        age: {
          type: DataTypes.INTEGER,
          field: "age",
          primaryKey: false,
          allowNull: true,
          unique: false,
          autoIncrement: false,
          defaultValue: undefined,
          onDelete: undefined,
          onUpdate: undefined,
        },
        status: {
          type: DataTypes.STRING,
          field: "status",
          primaryKey: false,
          allowNull: true,
          unique: false,
          autoIncrement: false,
          defaultValue: undefined,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
        },
      });
    });

    it("should handle minimal column definition", () => {
      const testAttributes: IAttibutes = {
        simpleColumn: {
          dataType: DataTypes.STRING,
        },
      };

      const result = formatAttributes(testAttributes);

      expect(result).toEqual({
        simpleColumn: {
          type: DataTypes.STRING,
          field: "simpleColumn",
          primaryKey: false,
          allowNull: true,
          unique: false,
          autoIncrement: false,
          defaultValue: undefined,
          onDelete: undefined,
          onUpdate: undefined,
        },
      });
    });
  });
});
