import { IWhereOptions, IFindOptions, Op } from "../types";

/**
 * Examples of using IWhereOptions for various query scenarios
 */

// Example 1: Simple equality matching
const simpleWhere: IWhereOptions = {
  id: 1,
  name: "<PERSON>",
  isActive: true,
};

// Example 2: Using comparison operators
const comparisonWhere: IWhereOptions = {
  age: {
    [Op.gte]: 18,
    [Op.lte]: 65,
  },
  salary: {
    [Op.gt]: 50000,
  },
  createdAt: {
    [Op.between]: ["2023-01-01", "2023-12-31"],
  },
};

// Example 3: Using logical operators
const logicalWhere: IWhereOptions = {
  [Op.and]: [
    {
      [Op.or]: [
        { department: "Engineering" },
        { department: "Product" },
      ],
    },
    {
      isActive: true,
    },
    {
      salary: {
        [Op.gte]: 60000,
      },
    },
  ],
};

// Example 4: String matching
const stringWhere: IWhereOptions = {
  email: {
    [Op.like]: "%@company.com",
  },
  firstName: {
    [Op.startsWith]: "<PERSON>",
  },
  lastName: {
    [Op.endsWith]: "son",
  },
  bio: {
    [Op.substring]: "developer",
  },
};

// Example 5: Array operations
const arrayWhere: IWhereOptions = {
  id: {
    [Op.in]: [1, 2, 3, 4, 5],
  },
  status: {
    [Op.notIn]: ["deleted", "suspended"],
  },
};

// Example 6: PostgreSQL specific operations
const postgresWhere: IWhereOptions = {
  tags: {
    [Op.contains]: ["javascript", "typescript"],
  },
  skills: {
    [Op.overlap]: ["react", "vue", "angular"],
  },
  name: {
    [Op.iLike]: "%JOHN%", // Case insensitive LIKE
  },
};

// Example 7: JSON column queries
const jsonWhere: IWhereOptions = {
  "metadata.preferences.theme": "dark",
  "profile.address.city": "New York",
  "settings.notifications.email": true,
};

// Example 8: Complex nested query
const complexWhere: IWhereOptions = {
  [Op.and]: [
    {
      [Op.or]: [
        {
          role: "admin",
        },
        {
          [Op.and]: [
            { role: "user" },
            { permissions: { [Op.contains]: ["read", "write"] } },
          ],
        },
      ],
    },
    {
      lastLoginAt: {
        [Op.gte]: new Date("2023-01-01"),
      },
    },
    {
      [Op.not]: {
        status: "banned",
      },
    },
  ],
};

// Example 9: Using WhereOptions in FindOptions
const findOptionsExample: IFindOptions = {
  where: {
    [Op.and]: [
      {
        isActive: true,
      },
      {
        [Op.or]: [
          { role: "admin" },
          { role: "moderator" },
        ],
      },
      {
        createdAt: {
          [Op.gte]: new Date("2023-01-01"),
        },
      },
    ],
  },
  attributes: ["id", "name", "email", "role"],
  order: [["createdAt", "DESC"]],
  limit: 50,
  offset: 0,
};

// Example 10: Range queries for dates and numbers
const rangeWhere: IWhereOptions = {
  // Date range
  createdAt: {
    [Op.between]: [
      new Date("2023-01-01"),
      new Date("2023-12-31"),
    ],
  },
  // Number range
  age: {
    [Op.between]: [18, 65],
  },
  // Exclude range
  score: {
    [Op.notBetween]: [0, 50],
  },
};

// Example 11: Regular expressions (PostgreSQL/MySQL)
const regexWhere: IWhereOptions = {
  email: {
    [Op.regexp]: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
  },
  phoneNumber: {
    [Op.regexp]: "^\\+?[1-9]\\d{1,14}$",
  },
};

// Example 12: Null checks
const nullWhere: IWhereOptions = {
  deletedAt: {
    [Op.is]: null, // IS NULL
  },
  isVerified: {
    [Op.is]: true, // IS TRUE
  },
  lastLoginAt: {
    [Op.ne]: null, // IS NOT NULL (using not equal)
  },
};

export {
  simpleWhere,
  comparisonWhere,
  logicalWhere,
  stringWhere,
  arrayWhere,
  postgresWhere,
  jsonWhere,
  complexWhere,
  findOptionsExample,
  rangeWhere,
  regexWhere,
  nullWhere,
};
