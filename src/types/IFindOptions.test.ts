import { IFindOptions, IndexHints } from "./index";

describe("IFindOptions", () => {
  it("should allow all basic query options", () => {
    const findOptions: IFindOptions = {
      where: { id: 1 },
      attributes: ["id", "name", "email"],
      limit: 10,
      offset: 0,
      order: [["name", "ASC"]],
      raw: false,
      nest: true,
      plain: false,
      paranoid: true,
      logging: console.log,
      benchmark: true,
    };

    expect(findOptions).toBeDefined();
    expect(findOptions.where).toEqual({ id: 1 });
    expect(findOptions.limit).toBe(10);
    expect(findOptions.offset).toBe(0);
  });

  it("should allow complex attributes selection", () => {
    const findOptions: IFindOptions = {
      attributes: {
        exclude: ["password", "createdAt"],
        include: ["id", "name", "email"],
      },
    };

    expect(findOptions.attributes).toBeDefined();
  });

  it("should allow index hints for MySQL", () => {
    const findOptions: IFindOptions = {
      indexHints: [
        { type: IndexHints.USE, values: ["idx_name"] },
        { type: IndexHints.FORCE, values: ["idx_email"] },
        { type: IndexHints.IGNORE, values: ["idx_old"] },
      ],
    };

    expect(findOptions.indexHints).toHaveLength(3);
  });

  it("should allow include options for associations", () => {
    const findOptions: IFindOptions = {
      include: [
        { model: "User", as: "author" },
        { model: "Category", as: "category" },
      ],
    };

    expect(findOptions.include).toBeDefined();
  });

  it("should allow transaction and pooling options", () => {
    const findOptions: IFindOptions = {
      transaction: null,
      useMaster: true,
      lock: true,
      skipLocked: true,
    };

    expect(findOptions.useMaster).toBe(true);
    expect(findOptions.lock).toBe(true);
    expect(findOptions.skipLocked).toBe(true);
  });

  it("should allow grouping and having options", () => {
    const findOptions: IFindOptions = {
      group: ["category_id"],
      having: { count: { $gt: 5 } },
      subQuery: false,
    };

    expect(findOptions.group).toEqual(["category_id"]);
    expect(findOptions.subQuery).toBe(false);
  });

  it("should allow parameter binding and replacements", () => {
    const findOptions: IFindOptions = {
      replacements: { name: "John" },
      bind: { id: 1 },
      mapToModel: true,
      fieldMap: { user_name: "name" },
    };

    expect(findOptions.replacements).toEqual({ name: "John" });
    expect(findOptions.bind).toEqual({ id: 1 });
    expect(findOptions.mapToModel).toBe(true);
  });

  it("should allow hooks and search path options", () => {
    const findOptions: IFindOptions = {
      hooks: false,
      searchPath: "public",
      retry: { max: 3 },
    };

    expect(findOptions.hooks).toBe(false);
    expect(findOptions.searchPath).toBe("public");
  });
});
