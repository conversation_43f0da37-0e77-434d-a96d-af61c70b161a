import { IWhereOptions, IWhereGeometryOptions, Op } from "./index";

describe("IWhereOptions", () => {
  it("should allow simple attribute matching", () => {
    const whereOptions: IWhereOptions = {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      isActive: true,
    };

    expect(whereOptions.id).toBe(1);
    expect(whereOptions.name).toBe("<PERSON>");
    expect(whereOptions.email).toBe("<EMAIL>");
    expect(whereOptions.isActive).toBe(true);
  });

  it("should allow logical operators", () => {
    const whereOptions: IWhereOptions = {
      [Op.and]: [
        { id: 1 },
        { name: "<PERSON>" },
      ],
      [Op.or]: [
        { email: "<EMAIL>" },
        { email: "<EMAIL>" },
      ],
      [Op.not]: {
        isDeleted: true,
      },
    };

    expect(whereOptions[Op.and]).toHaveLength(2);
    expect(whereOptions[Op.or]).toHaveLength(2);
    expect(whereOptions[Op.not]).toEqual({ isDeleted: true });
  });

  it("should allow comparison operators", () => {
    const whereOptions: IWhereOptions = {
      [Op.eq]: 5,
      [Op.ne]: 10,
      [Op.is]: null,
      [Op.gt]: 100,
      [Op.gte]: 50,
      [Op.lt]: 200,
      [Op.lte]: 150,
    };

    expect(whereOptions[Op.eq]).toBe(5);
    expect(whereOptions[Op.ne]).toBe(10);
    expect(whereOptions[Op.is]).toBeNull();
    expect(whereOptions[Op.gt]).toBe(100);
  });

  it("should allow range operators", () => {
    const whereOptions: IWhereOptions = {
      [Op.between]: [1, 10],
      [Op.notBetween]: [20, 30],
      [Op.in]: [1, 2, 3, 4, 5],
      [Op.notIn]: [6, 7, 8, 9, 10],
    };

    expect(whereOptions[Op.between]).toEqual([1, 10]);
    expect(whereOptions[Op.notBetween]).toEqual([20, 30]);
    expect(whereOptions[Op.in]).toEqual([1, 2, 3, 4, 5]);
    expect(whereOptions[Op.notIn]).toEqual([6, 7, 8, 9, 10]);
  });

  it("should allow string operators", () => {
    const whereOptions: IWhereOptions = {
      [Op.like]: "%john%",
      [Op.notLike]: "%admin%",
      [Op.iLike]: "%JOHN%",
      [Op.notILike]: "%ADMIN%",
      [Op.startsWith]: "john",
      [Op.endsWith]: "doe",
      [Op.substring]: "example",
    };

    expect(whereOptions[Op.like]).toBe("%john%");
    expect(whereOptions[Op.startsWith]).toBe("john");
    expect(whereOptions[Op.endsWith]).toBe("doe");
    expect(whereOptions[Op.substring]).toBe("example");
  });

  it("should allow regular expression operators", () => {
    const whereOptions: IWhereOptions = {
      [Op.regexp]: "^[a-z]+$",
      [Op.notRegexp]: "^[0-9]+$",
      [Op.iRegexp]: "^[A-Z]+$",
      [Op.notIRegexp]: "^[a-z0-9]+$",
    };

    expect(whereOptions[Op.regexp]).toBe("^[a-z]+$");
    expect(whereOptions[Op.notRegexp]).toBe("^[0-9]+$");
  });

  it("should allow PostgreSQL specific operators", () => {
    const whereOptions: IWhereOptions = {
      [Op.any]: [1, 2, 3],
      [Op.all]: [4, 5, 6],
      [Op.values]: [7, 8, 9],
    };

    expect(whereOptions[Op.any]).toEqual([1, 2, 3]);
    expect(whereOptions[Op.all]).toEqual([4, 5, 6]);
    expect(whereOptions[Op.values]).toEqual([7, 8, 9]);
  });

  it("should allow array and range operators", () => {
    const whereOptions: IWhereOptions = {
      [Op.contains]: [1, 2],
      [Op.contained]: [3, 4],
      [Op.overlap]: [5, 6],
      [Op.adjacent]: [7, 8],
      [Op.strictLeft]: [9, 10],
      [Op.strictRight]: [11, 12],
    };

    expect(whereOptions[Op.contains]).toEqual([1, 2]);
    expect(whereOptions[Op.contained]).toEqual([3, 4]);
    expect(whereOptions[Op.overlap]).toEqual([5, 6]);
  });

  it("should allow complex nested queries", () => {
    const whereOptions: IWhereOptions = {
      [Op.and]: [
        {
          [Op.or]: [
            { name: "John" },
            { name: "Jane" },
          ],
        },
        {
          age: {
            [Op.gte]: 18,
            [Op.lte]: 65,
          },
        },
        {
          email: {
            [Op.like]: "%@example.com",
          },
        },
      ],
    };

    expect(whereOptions[Op.and]).toHaveLength(3);
  });

  it("should allow column references and functions", () => {
    const whereOptions: IWhereOptions = {
      [Op.col]: "user.id",
      [Op.match]: "search_term",
    };

    expect(whereOptions[Op.col]).toBe("user.id");
    expect(whereOptions[Op.match]).toBe("search_term");
  });

  it("should allow nested attribute queries for JSON columns", () => {
    const whereOptions: IWhereOptions = {
      "metadata.name": "value",
      "settings.theme": "dark",
      "profile.address.city": "New York",
    };

    expect(whereOptions["metadata.name"]).toBe("value");
    expect(whereOptions["settings.theme"]).toBe("dark");
    expect(whereOptions["profile.address.city"]).toBe("New York");
  });
});

describe("IWhereGeometryOptions", () => {
  it("should allow geometry options", () => {
    const geometryOptions: IWhereGeometryOptions = {
      type: "Point",
      coordinates: [40.7128, -74.0060], // New York City coordinates
    };

    expect(geometryOptions.type).toBe("Point");
    expect(geometryOptions.coordinates).toEqual([40.7128, -74.0060]);
  });

  it("should allow polygon geometry", () => {
    const geometryOptions: IWhereGeometryOptions = {
      type: "Polygon",
      coordinates: [
        [
          [0, 0],
          [0, 1],
          [1, 1],
          [1, 0],
          [0, 0],
        ],
      ],
    };

    expect(geometryOptions.type).toBe("Polygon");
    expect(geometryOptions.coordinates).toHaveLength(1);
  });
});
