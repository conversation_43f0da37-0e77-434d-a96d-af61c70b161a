import {
  DataTypes as SequelizeDataTypes,
  DataType as SequelizeDataTypeInterface,
  IndexHints,
} from "sequelize";

const DataTypes = SequelizeDataTypes;
type IDataType = SequelizeDataTypeInterface;

interface IColumn {
  dataType: IDataType;
  columnName?: string;
  isPrimaryKey?: boolean;
  isNullable?: boolean;
  isUnique?: boolean;
  isAutoIncrement?: boolean;
  onDelete?: "CASCADE";
  onUpdate?: "CASCADE";
  defaultValue?: string | number | Date | boolean;
}

type IAttibutes = Record<string, IColumn>;

interface IModelOptions {}

type Dialect =
  | "mysql"
  | "postgres"
  | "sqlite"
  | "mariadb"
  | "mssql"
  | "db2"
  | "snowflake"
  | "oracle";

interface PoolOptions {
  /**
   * Maximum number of connections in pool. Default is 5
   */
  max?: number;

  /**
   * Minimum number of connections in pool. Default is 0
   */
  min?: number;

  /**
   * The maximum time, in milliseconds, that a connection can be idle before being released
   */
  idle?: number;

  /**
   * The maximum time, in milliseconds, that pool will try to get connection before throwing error
   */
  acquire?: number;

  /**
   * The time interval, in milliseconds, after which sequelize-pool will remove idle connections.
   */
  evict?: number;

  /**
   * The number of times to use a connection before closing and replacing it.  Default is Infinity
   */
  maxUses?: number;
}

interface IDbConfig {
  database: string;
  username: string;
  password: string;
  host: string;
  dialect: Dialect;
  port: number;
  pool: PoolOptions;
  schema: string;
  ssl?: boolean;
}

interface IFindOptions {
  // From Filterable interface
  /**
   * Attribute has to be matched for rows to be selected for the given action.
   */
  where?: any; // WhereOptions<TAttributes>

  // From Projectable interface
  /**
   * A list of the attributes that you want to select. To rename an attribute, you can pass an array, with
   * two elements - the first is the name of the attribute in the DB (or some kind of expression such as
   * `Sequelize.literal`, `Sequelize.fn` and so on), and the second is the name you want the attribute to
   * have in the returned instance
   */
  attributes?:
    | string[]
    | { exclude: string[]; include?: string[] }
    | { exclude?: string[]; include: string[] };

  // From Paranoid interface
  /**
   * If true, only non-deleted records will be returned. If false, both deleted and non-deleted records will
   * be returned. Only applies if `options.paranoid` is true for the model.
   */
  paranoid?: boolean;

  // From IndexHintable interface
  /**
   * MySQL only.
   */
  indexHints?: Array<{ type: IndexHints; values: string[] }>;

  // From Logging interface
  /**
   * A function that gets executed while running the query to log the sql.
   */
  logging?: boolean | ((sql: string, timing?: number) => void);

  /**
   * Pass query execution time in milliseconds as second argument to logging function (options.logging).
   */
  benchmark?: boolean;

  // From Transactionable interface
  /**
   * Transaction to run query under
   */
  transaction?: any; // Transaction | null

  // From Poolable interface
  /**
   * Force the query to use the write pool, regardless of the query type.
   *
   * @default false
   */
  useMaster?: boolean;

  // From QueryOptions interface
  /**
   * If true, sequelize will not try to format the results of the query, or build an instance of a model from
   * the result
   */
  raw?: boolean;

  /**
   * The type of query you are executing. The query type affects how results are formatted before they are
   * passed back. The type is a string, but `Sequelize.QueryTypes` is provided as convenience shortcuts.
   */
  type?: string;

  /**
   * If true, transforms objects with `.` separated property names into nested objects using
   * [dottie.js](https://github.com/mickhansen/dottie.js). For example { 'user.username': 'john' } becomes
   * { user: { username: 'john' }}. When `nest` is true, the query type is assumed to be `'SELECT'`,
   * unless otherwise specified
   *
   * @default false
   */
  nest?: boolean;

  /**
   * Sets the query type to `SELECT` and return a single row
   */
  plain?: boolean;

  /**
   * Either an object of named parameter replacements in the format `:param` or an array of unnamed
   * replacements to replace `?` in your SQL.
   */
  replacements?: { [key: string]: unknown } | unknown[];

  /**
   * Either an object of named parameter bindings in the format `$param` or an array of unnamed
   * values to bind to `$1`, `$2`, etc in your SQL.
   */
  bind?: { [key: string]: unknown } | unknown[];

  /**
   * Map returned fields to model's fields if `options.model` or `options.instance` is present.
   * Mapping will occur before building the model instance.
   */
  mapToModel?: boolean;

  /**
   * Map returned fields to arbitrary names for SELECT query type if `options.fieldMaps` is present.
   */
  fieldMap?: { [key: string]: string };

  // From FindOptions interface specific properties
  /**
   * A list of associations to eagerly load using a left join (a single association is also supported). Supported is either
   * `{ include: Model1 }`, `{ include: [ Model1, Model2, ...]}`, `{ include: [{ model: Model1, as: 'Alias' }]}` or
   * `{ include: [{ all: true }]}`.
   * If your association are set up with an `as` (eg. `X.hasMany(Y, { as: 'Z' }`, you need to specify Z in
   * the as attribute when eager loading Y).
   */
  include?: any | any[]; // Includeable | Includeable[]

  /**
   * Specifies an ordering. If a string is provided, it will be escaped. Using an array, you can provide
   * several columns / functions to order by. Each element can be further wrapped in a two-element array. The
   * first element is the column / function to order by, the second is the direction. For example:
   * `order: [['name', 'DESC']]`. In this way the column will be escaped, but the direction will not.
   */
  order?: any; // Order

  /**
   * GROUP BY in sql
   */
  group?: string | string[]; // GroupOption

  /**
   * Limits how many items will be retrieved by the operation.
   *
   * If `limit` and `include` are used together, Sequelize will turn the `subQuery` option on by default.
   * This is done to ensure that `limit` only impacts the Model on the same level as the `limit` option.
   *
   * You can disable this behavior by explicitly setting `subQuery: false`, however `limit` will then
   * affect the total count of returned values, including eager-loaded associations, instead of just one table.
   */
  limit?: number;

  /**
   * Skip the results;
   */
  offset?: number;

  /**
   * Lock the selected rows. Possible options are transaction.LOCK.UPDATE and transaction.LOCK.SHARE.
   * Postgres also supports transaction.LOCK.KEY_SHARE, transaction.LOCK.NO_KEY_UPDATE and specific model
   * locks with joins.
   */
  lock?: any; // LOCK | { level: LOCK; of: ModelStatic<Model> } | boolean

  /**
   * Skip locked rows. Only supported in Postgres.
   */
  skipLocked?: boolean;

  /**
   * Select group rows after groups and aggregates are computed.
   */
  having?: any; // WhereOptions<any>

  /**
   * Use sub queries (internal).
   *
   * If unspecified, this will `true` by default if `limit` is specified, and `false` otherwise.
   * See {@link FindOptions#limit} for more information.
   */
  subQuery?: boolean;

  // TODO: document this - this is an undocumented property but it exists and there are tests for it.
  groupedLimit?: unknown;

  // From Hookable interface
  /**
   * If `false` the applicable hooks will not be called.
   * The default value depends on the context.
   */
  hooks?: boolean;

  // Additional properties that might be useful
  /**
   * An optional parameter to specify the schema search_path (Postgres only)
   */
  searchPath?: string;

  /**
   * Retry options for the query
   */
  retry?: any; // RetryOptions
}

export {
  IColumn,
  IAttibutes,
  DataTypes,
  IDataType,
  IModelOptions,
  IDbConfig,
  Dialect,
  PoolOptions,
  IFindOptions,
};
