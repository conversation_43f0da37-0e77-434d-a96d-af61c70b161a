import { SequelizeModelController } from "../adaptors/sequelizer";
import { IModelOptions, IAttibutes, IFindOptions } from "../types";

export class Model {
  private readonly entityName: string;
  private readonly attributes: IAttibutes;
  private readonly options: IModelOptions;
  private sequelizerModel?: SequelizeModelController;

  constructor(
    entityName: string,
    attributes: IAttibutes,
    options?: IModelOptions
  ) {
    this.entityName = entityName;
    this.attributes = attributes;
    this.options = options || {};
  }
  public getEntityName() {
    return this.entityName;
  }
  public getAttributes() {
    return this.attributes;
  }
  public getOptions() {
    return this.options;
  }
  public setSequelizerModel(model: SequelizeModelController) {
    return (this.sequelizerModel = model);
  }
  public findAll(findOptions: IFindOptions) {
    if (!this.sequelizerModel) {
      throw new Error("Model not set");
    }
    return this.sequelizerModel.findAll(findOptions);
  }
}
