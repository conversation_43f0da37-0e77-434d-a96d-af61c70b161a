import { Sequelize } from "sequelize";
import { DataTypes, IModelOptions, IAttibutes } from "../types";

const sequelize = new Sequelize({});

const User = sequelize.define(
  "User",
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      onDelete: "CASCADE",
    },
  },
  {
    sequelize,
    tableName: "User",
  }
);

User.belongsTo;

sequelize.createSchema();

export class Model {
  private readonly entityName: string;
  private readonly attributes: IAttibutes;
  private readonly options: IModelOptions;

  constructor(
    entityName: string,
    attributes: IAttibutes,
    options?: IModelOptions
  ) {
    this.entityName = entityName;
    this.attributes = attributes;
    this.options = options || {};
  }
}
