import { IAttibutes, IDbConfig, IModelOptions } from "../types";
import { Model } from "./model";

export class DbSchema {
  private readonly schemaName: string;
  private entities: Model[] = [];
  private dbConfig = {};

  constructor(schemaName: string, dbConfig: IDbConfig) {
    this.schemaName = schemaName;
    this.dbConfig = dbConfig;
  }

  public setEntity(model: Model): Model {
    this.entities.push(model);
    return model;
  }
  public defineEntity(
    entityName: string,
    attributes: IAttibutes,
    options?: IModelOptions
  ) {
    const model = new Model(entityName, attributes, options);
    this.entities.push(model);
    return model;
  }
}
