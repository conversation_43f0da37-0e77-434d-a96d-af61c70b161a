import { IAttibutes, IDbConfig, IModelOptions } from "../types";
import { Model } from "./model";
import { SequelizerAdaptor } from "../adaptors";

export class DbSchema {
  private entities: Model[] = [];
  private dbConfig = {};
  private sequelizerAdaptor: SequelizerAdaptor;

  constructor(dbConfig: IDbConfig) {
    this.dbConfig = dbConfig;
    this.sequelizerAdaptor = new SequelizerAdaptor(dbConfig);
  }

  public setEntity(model: Model): Model {
    this.entities.push(model);
    const seqModel = this.sequelizerAdaptor.define(
      model.getEntityName(),
      model.getAttributes(),
      model.getOptions()
    );
    return model;
  }

  public defineEntity(
    entityName: string,
    attributes: IAttibutes,
    options?: IModelOptions
  ) {
    const model = new Model(entityName, attributes, options);
    this.entities.push(model);
    const seqModel = this.sequelizerAdaptor.define(
      entityName,
      attributes,
      options
    );
    return model;
  }

  public getDbConfig() {
    return this.dbConfig;
  }
}
